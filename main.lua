--[[ Full script: Multi-select + improved reliability + Tabbed Interface (Shop + Miscellaneous + Event)
   + English loading UI (no demo text)
   + Fake loading 2s BEFORE allowing main GUI to show
   + Do NOT show main GUI until both fake loading and initial populate finished
   + Added Pet Egg functionality with toggle buttons
   + Added Event tab with cooking automation

   PERFORMANCE OPTIMIZATIONS:
   + Stock checking with intelligent caching (2s cache duration)
   + Conditional shop updates only when shops are open
   + Reduced update intervals (1.5s stock, 1s events vs 0.6s/0.5s)
   + Limited GetDescendants() searches for better performance
   + Debounced GUI updates to prevent excessive refreshes
   + Optimized selected items counting with fast lookups
   + Early exit conditions in automation loops
   + Reduced wait times in event automation phases
   + Performance monitoring and statistics
]]
--// Services
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local MarketplaceService = game:GetService("MarketplaceService")
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local TweenService = game:GetService("TweenService")

local LocalPlayer = Players.LocalPlayer
local PlayerGui = LocalPlayer:WaitForChild("PlayerGui")
local player = LocalPlayer

-- === Loading GUI (show immediately) ===
local LoadingGUI = Instance.new("ScreenGui")
LoadingGUI.Name = "DepsoAutoBuyLoading"
LoadingGUI.ResetOnSpawn = false
LoadingGUI.Parent = PlayerGui
LoadingGUI.ZIndexBehavior = Enum.ZIndexBehavior.Sibling

local loadingOverlay = Instance.new("Frame")
loadingOverlay.Size = UDim2.new(1, 0, 1, 0)
loadingOverlay.Position = UDim2.new(0, 0, 0, 0)
loadingOverlay.BackgroundColor3 = Color3.fromRGB(0,0,0)
loadingOverlay.BackgroundTransparency = 0.55
loadingOverlay.BorderSizePixel = 0
loadingOverlay.Parent = LoadingGUI

local loadingBox = Instance.new("Frame")
loadingBox.Size = UDim2.new(0, 520, 0, 160) -- bigger box
loadingBox.Position = UDim2.new(0.5, -260, 0.5, -80)
loadingBox.BackgroundColor3 = Color3.fromRGB(28,28,28)
loadingBox.BorderSizePixel = 0
loadingBox.Parent = LoadingGUI
local UICorner = Instance.new("UICorner", loadingBox)
UICorner.CornerRadius = UDim.new(0, 12)

local loadingTitle = Instance.new("TextLabel")
loadingTitle.Size = UDim2.new(1, -24, 0, 48)
loadingTitle.Position = UDim2.new(0, 12, 0, 8)
loadingTitle.BackgroundTransparency = 1
loadingTitle.Text = "Loading..."
loadingTitle.TextColor3 = Color3.fromRGB(250,250,250)
loadingTitle.Font = Enum.Font.GothamBold
loadingTitle.TextSize = 28
loadingTitle.TextXAlignment = Enum.TextXAlignment.Center
loadingTitle.Parent = loadingBox

local loadingLabel = Instance.new("TextLabel")
loadingLabel.Size = UDim2.new(1, -24, 0, 28)
loadingLabel.Position = UDim2.new(0, 12, 0, 62)
loadingLabel.BackgroundTransparency = 1
loadingLabel.Text = "Preparing UI — please wait"
loadingLabel.TextColor3 = Color3.fromRGB(200,200,200)
loadingLabel.Font = Enum.Font.Gotham
loadingLabel.TextSize = 16
loadingLabel.TextXAlignment = Enum.TextXAlignment.Center
loadingLabel.Parent = loadingBox

-- animated dots
local loadingRunning = true
task.spawn(function()
    local dots = 0
    while loadingRunning do
        dots = dots + 1
        if dots > 3 then dots = 0 end
        local s = string.rep(".", dots)
        pcall(function() loadingTitle.Text = "Loading" .. s end)
        task.wait(0.45)
    end
end)

-- fake loading duration (seconds)
local FAKE_LOADING_SECONDS = 2
local fakeDone = false
task.spawn(function()
    task.wait(FAKE_LOADING_SECONDS)
    fakeDone = true
end)

-- continue with main script while LoadingGUI still visible
local GameInfo = (pcall(function() return MarketplaceService:GetProductInfo(game.PlaceId) end) and MarketplaceService:GetProductInfo(game.PlaceId)) or { Name = "Grow a Garden" }

--// Data
local SeedStock = {}
local GearStock = {}
local PetEggStock = {}
local SelectedSeeds = {} -- SelectedSeeds[name] = true/false
local SelectedGear = {} -- SelectedGear[name] = true/false
local SelectedPetEggs = {} -- SelectedPetEggs[name] = true/false

-- Event tab data
local SelectedEventSeed = ""
local PlantQueue = {}
local MAX_PLANTS = 5

-- Performance optimization variables
local stockCache = {
    seeds = { data = {}, lastUpdate = 0, isValid = false },
    gear = { data = {}, lastUpdate = 0, isValid = false },
    petEggs = { data = {}, lastUpdate = 0, isValid = false }
}
local STOCK_CACHE_DURATION = 2 -- Cache for 2 seconds
local STOCK_UPDATE_INTERVAL = 1.5 -- Update every 1.5 seconds instead of 0.6
local EVENT_LOOP_INTERVAL = 1 -- Event loop every 1 second instead of 0.5
local lastShopCheck = { seeds = 0, gear = 0, petEggs = 0 }
local SHOP_CHECK_COOLDOWN = 0.5 -- Only check if shop was accessed recently
local performanceStats = { stockChecks = 0, cacheHits = 0, lastReset = tick() }

--// Remotes
local GameEvents = ReplicatedStorage:WaitForChild("GameEvents")

local function BuySeed(Seed: string)
    pcall(function()
        GameEvents.BuySeedStock:FireServer(Seed)
    end)
end

local function BuyGear(Gear: string)
    pcall(function()
        GameEvents.BuyGearStock:FireServer(Gear)
    end)
end

local function BuyPetEgg(EggName: string)
    pcall(function()
        GameEvents.BuyPetEgg:FireServer(EggName)
    end)
end

local function BuyAllSelectedSeeds()
    -- Fast check using optimized counter
    if not HasSelectedItems("seeds") then return end

    local selectedList = {}
    for name, sel in pairs(SelectedSeeds) do
        if sel then table.insert(selectedList, name) end
    end

    for _, seedName in ipairs(selectedList) do
        local stock = SeedStock[seedName] or 0
        if stock > 0 then
            for i = 1, stock do
                BuySeed(seedName)
                task.wait(0.08)
            end
        end
    end
end

local function BuyAllSelectedGear()
    -- Fast check using optimized counter
    if not HasSelectedItems("gear") then return end

    local selectedList = {}
    for name, sel in pairs(SelectedGear) do
        if sel then table.insert(selectedList, name) end
    end

    for _, gearName in ipairs(selectedList) do
        local stock = GearStock[gearName] or 0
        if stock > 0 then
            for i = 1, stock do
                BuyGear(gearName)
                task.wait(0.08)
            end
        end
    end
end

local function BuyAllSelectedPetEggs()
    -- Fast check using optimized counter
    if not HasSelectedItems("petEggs") then return end

    local selectedList = {}
    for name, sel in pairs(SelectedPetEggs) do
        if sel then table.insert(selectedList, name) end
    end

    for _, eggName in ipairs(selectedList) do
        BuyPetEgg(eggName)
        task.wait(0.15) -- Slightly longer delay for pet eggs
    end
end

-- Event functions
local PlantsInPot = 0

local function FindAndEquipSeed(seedName)
    local success = pcall(function()
        local backpack = game:GetService("Players").LocalPlayer.Backpack
        for _, item in pairs(backpack:GetChildren()) do
            if item.Name:lower():find(seedName:lower()) then
                item.Parent = game:GetService("Players").LocalPlayer.Character
                return true
            end
        end
        return false
    end)
    return success
end

local function SubmitPlant()
    pcall(function()
        local args = {"SubmitHeldPlant"}
        game:GetService("ReplicatedStorage"):WaitForChild("GameEvents"):WaitForChild("CookingPotService_RE"):FireServer(unpack(args))
    end)
end

local function CookBest()
    pcall(function()
        local args = {"CookBest"}
        game:GetService("ReplicatedStorage"):WaitForChild("GameEvents"):WaitForChild("CookingPotService_RE"):FireServer(unpack(args))
    end)
end

local function GetFoodFromPot()
    pcall(function()
        local args = {"GetFoodFromPot"}
        game:GetService("ReplicatedStorage"):WaitForChild("GameEvents"):WaitForChild("CookingPotService_RE"):FireServer(unpack(args))
    end)
end

local function SubmitFood()
    pcall(function()
        local args = {"SubmitHeldFood"}
        game:GetService("ReplicatedStorage"):WaitForChild("GameEvents"):WaitForChild("SubmitFoodService_RE"):FireServer(unpack(args))
    end)
end

-- Check if cooking timer is finished
local function IsCookingFinished()
    local success, result = pcall(function()
        local timerText = game:GetService("Players").LocalPlayer.PlayerGui.CookingTimerBB.Timer.Text
        return timerText == "Ready!" or timerText == "Ready" or timerText == "00:00:00"
    end)
    return success and result
end

-- Count current plants in queue
local function CountPlantsInQueue()
    return PlantsInPot
end

-- Check if selected seed exists in backpack
local function HasSelectedSeed()
    if SelectedEventSeed == "" then return false end
    
    local success, result = pcall(function()
        local backpack = game:GetService("Players").LocalPlayer.Backpack
        for _, item in pairs(backpack:GetChildren()) do
            if item.Name:lower():find(SelectedEventSeed:lower()) then
                return true
            end
        end
        return false
    end)
    return success and result
end

-- Optimized Seed reader with caching
local function GetSeedStock(forceUpdate)
    local now = tick()

    -- Return cached data if still valid and not forcing update
    if not forceUpdate and stockCache.seeds.isValid and (now - stockCache.seeds.lastUpdate) < STOCK_CACHE_DURATION then
        performanceStats.cacheHits = performanceStats.cacheHits + 1
        return stockCache.seeds.data
    end

    -- Check if seed shop is actually open
    local SeedShop = PlayerGui:FindFirstChild("Seed_Shop")
    if not SeedShop then
        -- If shop not open, return cached data if available
        if stockCache.seeds.isValid then
            return stockCache.seeds.data
        end
        return {}
    end

    -- Update last shop check time
    lastShopCheck.seeds = now
    performanceStats.stockChecks = performanceStats.stockChecks + 1

    local sample = SeedShop:FindFirstChild("Blueberry", true)
    if not sample then return stockCache.seeds.data end
    local Items = sample.Parent

    local NewList = {}
    for _, Item in next, Items:GetChildren() do
        local MainFrame = Item:FindFirstChild("Main_Frame")
        if not MainFrame then continue end
        local StockText = MainFrame:FindFirstChild("Stock_Text") and MainFrame.Stock_Text.Text or "0"
        local StockCount = tonumber(StockText:match("%d+")) or 0
        SeedStock[Item.Name] = StockCount
        NewList[Item.Name] = StockCount
    end

    -- Update cache
    stockCache.seeds.data = NewList
    stockCache.seeds.lastUpdate = now
    stockCache.seeds.isValid = true

    return NewList
end

-- Optimized Gear reader with caching
local function GetGearStock(forceUpdate)
    local now = tick()

    -- Return cached data if still valid and not forcing update
    if not forceUpdate and stockCache.gear.isValid and (now - stockCache.gear.lastUpdate) < STOCK_CACHE_DURATION then
        performanceStats.cacheHits = performanceStats.cacheHits + 1
        return stockCache.gear.data
    end

    -- Check if gear shop is actually open
    local GearShop = PlayerGui:FindFirstChild("Gear_Shop")
    if not GearShop then
        -- If shop not open, return cached data if available
        if stockCache.gear.isValid then
            return stockCache.gear.data
        end
        return {}
    end

    -- Update last shop check time
    lastShopCheck.gear = now
    performanceStats.stockChecks = performanceStats.stockChecks + 1

    -- Try to find any gear item to locate the parent container (optimized)
    local sample = nil
    local descendants = GearShop:GetDescendants()
    for i = 1, math.min(#descendants, 50) do -- Limit search to first 50 descendants for performance
        local child = descendants[i]
        if child:FindFirstChild("Main_Frame") and child:FindFirstChild("Main_Frame"):FindFirstChild("Stock_Text") then
            sample = child
            break
        end
    end

    if not sample then return stockCache.gear.data end
    local Items = sample.Parent

    local NewList = {}
    for _, Item in next, Items:GetChildren() do
        local MainFrame = Item:FindFirstChild("Main_Frame")
        if not MainFrame then continue end
        local StockText = MainFrame:FindFirstChild("Stock_Text") and MainFrame.Stock_Text.Text or "0"
        local StockCount = tonumber(StockText:match("%d+")) or 0
        GearStock[Item.Name] = StockCount
        NewList[Item.Name] = StockCount
    end

    -- Update cache
    stockCache.gear.data = NewList
    stockCache.gear.lastUpdate = now
    stockCache.gear.isValid = true

    return NewList
end

-- Optimized Pet Egg reader with caching
local function GetPetEggStock(forceUpdate)
    local now = tick()

    -- Return cached data if still valid and not forcing update
    if not forceUpdate and stockCache.petEggs.isValid and (now - stockCache.petEggs.lastUpdate) < STOCK_CACHE_DURATION then
        performanceStats.cacheHits = performanceStats.cacheHits + 1
        return stockCache.petEggs.data
    end

    -- Check if pet shop is actually open
    local PetShopUI = PlayerGui:FindFirstChild("PetShop_UI")
    if not PetShopUI then
        -- If shop not open, return cached data if available
        if stockCache.petEggs.isValid then
            return stockCache.petEggs.data
        end
        return {}
    end

    -- Update last shop check time
    lastShopCheck.petEggs = now
    performanceStats.stockChecks = performanceStats.stockChecks + 1

    local NewList = {}
    -- Optimized search - limit descendants to improve performance
    local descendants = PetShopUI:GetDescendants()
    for i = 1, math.min(#descendants, 100) do -- Limit search for performance
        local child = descendants[i]
        if child:IsA("Frame") or child:IsA("TextButton") then
            local eggName = nil

            -- Check if it's a proper egg item (must contain "Egg" and not be UI elements)
            if child.Name:match(".*Egg$") and not child.Name:match("Shop") and not child.Name:match("Button") and not child.Name:match("Frame") then
                eggName = child.Name
            elseif child:FindFirstChild("EggName") and child.EggName:IsA("TextLabel") then
                local eggText = child.EggName.Text
                if eggText:match(".*Egg$") and not eggText:match("Shop") then
                    eggName = eggText
                end
            elseif child:FindFirstChildOfClass("TextLabel") then
                local textLabel = child:FindFirstChildOfClass("TextLabel")
                local labelText = textLabel.Text
                -- Only accept if it ends with "Egg" and doesn't contain UI-related terms
                if labelText:match(".*Egg$") and not labelText:match("Shop") and not labelText:match("Button") and labelText ~= "Pet Eggs" then
                    eggName = labelText
                end
            end

            -- Additional filtering to exclude obvious UI elements
            if eggName and eggName ~= "" and eggName ~= "Pet Egg Shop" and not eggName:match("^Pet Egg") then
                PetEggStock[eggName] = 1
                NewList[eggName] = 1
            end
        end
    end

    -- Remove duplicates and filter out non-egg items
    local filteredList = {}
    for name, count in pairs(NewList) do
        -- Final check: must end with "Egg" and not be a UI element
        if name:match(".*Egg$") and not name:match("Shop") and name ~= "Pet Eggs" then
            filteredList[name] = count
            PetEggStock[name] = count
        end
    end

    -- Update cache
    stockCache.petEggs.data = filteredList
    stockCache.petEggs.lastUpdate = now
    stockCache.petEggs.isValid = true

    return filteredList
end

-- Helper: remove other custom GUIs (best-effort)
local function RemoveOtherCustomGUIs(keepThisName)
    for _, child in ipairs(PlayerGui:GetChildren()) do
        if child:IsA("ScreenGui") and child.Name ~= keepThisName then
            local foundWindow = child:FindFirstChild("Window", true)
            if foundWindow then
                for _, desc in ipairs(child:GetDescendants()) do
                    if (desc:IsA("LocalScript") or desc:IsA("Script")) and desc.Parent then
                        pcall(function()
                            if desc.Disabled ~= nil then desc.Disabled = true end
                        end)
                    end
                end
                pcall(function() child:Destroy() end)
            end
        end
    end
end

-- Performance monitoring function
local function ResetPerformanceStats()
    local now = tick()
    if now - performanceStats.lastReset > 60 then -- Reset every minute
        print(string.format("Performance Stats: Stock checks: %d, Cache hits: %d, Hit rate: %.1f%%",
            performanceStats.stockChecks,
            performanceStats.cacheHits,
            performanceStats.stockChecks > 0 and (performanceStats.cacheHits / (performanceStats.stockChecks + performanceStats.cacheHits) * 100) or 0
        ))
        performanceStats.stockChecks = 0
        performanceStats.cacheHits = 0
        performanceStats.lastReset = now
    end
end

-- Debounced update function to prevent excessive GUI updates
local lastGUIUpdate = 0
local GUI_UPDATE_DEBOUNCE = 0.3
local function ShouldUpdateGUI()
    local now = tick()
    if now - lastGUIUpdate > GUI_UPDATE_DEBOUNCE then
        lastGUIUpdate = now
        return true
    end
    return false
end

-- Optimized selected items counting to avoid repeated table iterations
local selectedCounts = { seeds = 0, gear = 0, petEggs = 0 }
local function UpdateSelectedCount(category, name, selected)
    if category == "seeds" then
        if selected and not SelectedSeeds[name] then
            selectedCounts.seeds = selectedCounts.seeds + 1
        elseif not selected and SelectedSeeds[name] then
            selectedCounts.seeds = selectedCounts.seeds - 1
        end
        SelectedSeeds[name] = selected
    elseif category == "gear" then
        if selected and not SelectedGear[name] then
            selectedCounts.gear = selectedCounts.gear + 1
        elseif not selected and SelectedGear[name] then
            selectedCounts.gear = selectedCounts.gear - 1
        end
        SelectedGear[name] = selected
    elseif category == "petEggs" then
        if selected and not SelectedPetEggs[name] then
            selectedCounts.petEggs = selectedCounts.petEggs + 1
        elseif not selected and SelectedPetEggs[name] then
            selectedCounts.petEggs = selectedCounts.petEggs - 1
        end
        SelectedPetEggs[name] = selected
    end
end

-- Fast check for any selected items
local function HasSelectedItems(category)
    return selectedCounts[category] > 0
end

RemoveOtherCustomGUIs("DepsoAutoBuyUI_temp")

--// UI setup (create UI and parent now, but hide main window until ready)
local UI = Instance.new("ScreenGui")
UI.Name = "DepsoAutoBuyUI"
UI.ResetOnSpawn = false
UI.Parent = PlayerGui
UI.ZIndexBehavior = Enum.ZIndexBehavior.Sibling

-- remove other GUIs (skip ours)
RemoveOtherCustomGUIs(UI.Name)

local function round(px)
    local cl = Instance.new("UICorner")
    cl.CornerRadius = UDim.new(0, px)
    return cl
end

local colors = {
    Window = Color3.fromRGB(30, 30, 30),
    Accent = Color3.fromRGB(70, 140, 40),
    AccentHover = Color3.fromRGB(90, 160, 60),
    Sub = Color3.fromRGB(40, 40, 40),
    Text = Color3.fromRGB(230, 230, 230),
    TabInactive = Color3.fromRGB(50, 50, 50),
}

-- Tab System Variables
local currentTab = "Shop"
local tabContents = {}

local function CreateWindow(title)
    local w = Instance.new("Frame")
    w.Name = "Window"
    w.Size = UDim2.new(0, 600, 0, 450) -- wider for 3 tabs
    w.Position = UDim2.new(0, 20, 0, 60)
    w.BackgroundColor3 = colors.Window
    w.BorderSizePixel = 0
    w.Parent = UI
    round(10):Clone().Parent = w

    local top = Instance.new("Frame")
    top.Name = "Top"
    top.Size = UDim2.new(1, 0, 0, 40)
    top.BackgroundColor3 = colors.Accent
    top.BorderSizePixel = 0
    top.Parent = w
    round(10):Clone().Parent = top

    local titleLbl = Instance.new("TextLabel")
    titleLbl.Size = UDim2.new(1, -84, 1, 0)
    titleLbl.Position = UDim2.new(0, 12, 0, 0)
    titleLbl.BackgroundTransparency = 1
    titleLbl.Text = title
    titleLbl.TextColor3 = colors.Text
    titleLbl.Font = Enum.Font.GothamBold
    titleLbl.TextSize = 16
    titleLbl.TextXAlignment = Enum.TextXAlignment.Left
    titleLbl.Parent = top

    local minimizeBtn = Instance.new("TextButton")
    minimizeBtn.Name = "Minimize"
    minimizeBtn.Size = UDim2.new(0, 40, 0, 26)
    minimizeBtn.Position = UDim2.new(1, -56, 0, 7)
    minimizeBtn.BackgroundColor3 = colors.Sub
    minimizeBtn.BorderSizePixel = 0
    minimizeBtn.Text = "-"
    minimizeBtn.TextColor3 = colors.Text
    minimizeBtn.Font = Enum.Font.GothamBold
    minimizeBtn.TextSize = 18
    minimizeBtn.Parent = top
    round(8):Clone().Parent = minimizeBtn

    -- Tab Container
    local tabContainer = Instance.new("Frame")
    tabContainer.Name = "TabContainer"
    tabContainer.Size = UDim2.new(1, -24, 0, 36)
    tabContainer.Position = UDim2.new(0, 12, 0, 48)
    tabContainer.BackgroundTransparency = 1
    tabContainer.Parent = w

    -- Content Container
    local content = Instance.new("Frame")
    content.Name = "Content"
    content.Size = UDim2.new(1, -24, 1, -120) -- adjusted for tabs
    content.Position = UDim2.new(0, 12, 0, 88)
    content.BackgroundColor3 = colors.Sub
    content.BorderSizePixel = 0
    content.Parent = w
    round(8):Clone().Parent = content

    return { Window = w, Top = top, TabContainer = tabContainer, Content = content, Title = titleLbl, Minimize = minimizeBtn }
end

local function CreateTab(parent, text, tabName, isActive, position)
    local tab = Instance.new("TextButton")
    tab.Name = tabName
    tab.Size = UDim2.new(0, 120, 1, 0)
    tab.Position = UDim2.new(0, position, 0, 0)
    tab.BackgroundColor3 = isActive and colors.Accent or colors.TabInactive
    tab.BorderSizePixel = 0
    tab.Text = text
    tab.TextColor3 = colors.Text
    tab.Font = Enum.Font.GothamBold
    tab.TextSize = 14
    tab.Parent = parent
    round(8):Clone().Parent = tab
    
    return tab
end

local function CreateLabel(parent, text, y)
    local lbl = Instance.new("TextLabel")
    lbl.Size = UDim2.new(1, -24, 0, 20)
    lbl.Position = UDim2.new(0, 12, 0, y or 6)
    lbl.BackgroundTransparency = 1
    lbl.Text = text
    lbl.TextColor3 = colors.Text
    lbl.Font = Enum.Font.Gotham
    lbl.TextSize = 13
    lbl.TextXAlignment = Enum.TextXAlignment.Left
    lbl.Parent = parent
    return lbl
end

local function CreateButton(parent, text, y, callback)
    local btn = Instance.new("TextButton")
    btn.Size = UDim2.new(1, -24, 0, 36)
    btn.Position = UDim2.new(0, 12, 0, y)
    btn.BackgroundColor3 = colors.Accent
    btn.Text = text
    btn.TextColor3 = Color3.new(1,1,1)
    btn.Font = Enum.Font.GothamBold
    btn.TextSize = 14
    btn.BorderSizePixel = 0
    btn.Parent = parent
    round(8):Clone().Parent = btn
    btn.MouseButton1Click:Connect(function() pcall(callback) end)
    return btn
end

local function CreateCheckbox(parent, label, initial, y)
    local container = Instance.new("Frame")
    container.Size = UDim2.new(1, -24, 0, 30)
    container.Position = UDim2.new(0, 12, 0, y)
    container.BackgroundTransparency = 1
    container.Parent = parent

    local box = Instance.new("TextButton")
    box.Size = UDim2.new(0, 24, 0, 24)
    box.Position = UDim2.new(0, 6, 0, 3)
    box.BackgroundColor3 = colors.Window
    box.BorderSizePixel = 0
    box.Text = ""
    box.Parent = container
    round(6):Clone().Parent = box

    local lbl = Instance.new("TextLabel")
    lbl.Size = UDim2.new(1, -48, 1, 0)
    lbl.Position = UDim2.new(0, 40, 0, 0)
    lbl.BackgroundTransparency = 1
    lbl.Text = label
    lbl.TextColor3 = colors.Text
    lbl.Font = Enum.Font.Gotham
    lbl.TextSize = 13
    lbl.TextXAlignment = Enum.TextXAlignment.Left
    lbl.Parent = container

    local checked = Instance.new("Frame")
    checked.Size = UDim2.new(1, -6, 1, -6)
    checked.Position = UDim2.new(0, 3, 0, 3)
    checked.BackgroundColor3 = colors.Accent
    checked.Visible = initial
    checked.Parent = box
    round(4):Clone().Parent = checked

    local state = { Value = initial }
    box.MouseButton1Click:Connect(function()
        state.Value = not state.Value
        checked.Visible = state.Value
    end)

    return state, container
end

-- CreateCombo with improved selection reliability
local function CreateCombo(parent, label, getItems, selectedTable, y, singleSelect)
    local container = Instance.new("Frame")
    container.Size = UDim2.new(1, -24, 0, 80)
    container.Position = UDim2.new(0, 12, 0, y)
    container.BackgroundTransparency = 1
    container.Parent = parent

    local lbl = Instance.new("TextLabel")
    lbl.Size = UDim2.new(1, 0, 0, 18)
    lbl.Position = UDim2.new(0, 0, 0, 0)
    lbl.BackgroundTransparency = 1
    lbl.Text = label
    lbl.TextColor3 = colors.Text
    lbl.Font = Enum.Font.Gotham
    lbl.TextSize = 13
    lbl.TextXAlignment = Enum.TextXAlignment.Left
    lbl.Parent = container

    local box = Instance.new("TextButton")
    box.Size = UDim2.new(1, 0, 0, 36)
    box.Position = UDim2.new(0, 0, 0, 22)
    box.BackgroundColor3 = colors.Window
    box.BorderSizePixel = 0
    box.Text = ""
    box.Active = true
    box.Parent = container
    round(8):Clone().Parent = box

    local selectedLabel = Instance.new("TextLabel")
    selectedLabel.Size = UDim2.new(1, -12, 1, 0)
    selectedLabel.Position = UDim2.new(0, 8, 0, 0)
    selectedLabel.BackgroundTransparency = 1
    selectedLabel.Text = singleSelect and "(select)" or "(select)"
    selectedLabel.TextColor3 = colors.Text
    selectedLabel.Font = Enum.Font.Gotham
    selectedLabel.TextSize = 13
    selectedLabel.TextXAlignment = Enum.TextXAlignment.Left
    selectedLabel.Parent = box

    local dropdown = Instance.new("Frame")
    dropdown.Name = "ComboDropdown"
    dropdown.Size = UDim2.fromOffset(260, 120)
    dropdown.Position = UDim2.fromOffset(0, 0)
    dropdown.BackgroundColor3 = colors.Sub
    dropdown.BorderSizePixel = 0
    dropdown.Visible = false
    dropdown.ZIndex = 50
    dropdown.Parent = UI
    round(8):Clone().Parent = dropdown

    local shadow = Instance.new("Frame")
    shadow.Size = UDim2.new(1, 6, 1, 6)
    shadow.Position = UDim2.new(0, -3, 0, -3)
    shadow.BackgroundTransparency = 0.88
    shadow.BackgroundColor3 = Color3.new(0,0,0)
    shadow.BorderSizePixel = 0
    shadow.Parent = dropdown
    round(10):Clone().Parent = shadow
    shadow.ZIndex = 49

    local scroll = Instance.new("ScrollingFrame")
    scroll.Size = UDim2.new(1, -8, 1, -8)
    scroll.Position = UDim2.new(0, 4, 0, 4)
    scroll.BackgroundTransparency = 1
    scroll.BorderSizePixel = 0
    scroll.Parent = dropdown
    scroll.ScrollBarThickness = 8
    scroll.AutomaticCanvasSize = Enum.AutomaticSize.Y
    scroll.ZIndex = 51

    local uiList = Instance.new("UIListLayout")
    uiList.Parent = scroll
    uiList.Padding = UDim.new(0, 6)
    uiList.SortOrder = Enum.SortOrder.LayoutOrder

    local open = false
    local isPopulating = false
    local lastPopulate = 0

    local function clampDropdownToScreen(px, py, w, h)
        local cam = workspace.CurrentCamera
        local screenW = cam and cam.ViewportSize.X or 800
        local screenH = cam and cam.ViewportSize.Y or 600
        if px + w > screenW then px = math.max(4, screenW - w - 4) end
        if py + h > screenH then py = math.max(4, screenH - h - 4) end
        return px, py
    end

    local function updateSelectedLabel()
        if singleSelect then
            -- For single select, show the selected item or "(select)"
            if type(selectedTable) == "table" then
                -- If it's a table (like for multi-select), find the first selected item
                for name, sel in pairs(selectedTable) do
                    if sel then 
                        selectedLabel.Text = name
                        return
                    end
                end
                selectedLabel.Text = "(select)"
            else
                -- If it's a string variable (like SelectedEventSeed)
                selectedLabel.Text = selectedTable ~= "" and selectedTable or "(select)"
            end
        else
            -- Multi-select logic
            local names = {}
            for name, sel in pairs(selectedTable) do
                if sel then table.insert(names, name) end
            end
            if #names == 0 then
                selectedLabel.Text = "(select)"
            elseif #names <= 3 then
                selectedLabel.Text = table.concat(names, ", ")
            else
                selectedLabel.Text = names[1] .. ", " .. names[2] .. " +" .. tostring(#names - 2)
            end
        end
    end

    local function populate()
        if isPopulating then return end
        isPopulating = true
        lastPopulate = tick()

        for _, child in next, scroll:GetChildren() do
            if child:IsA("Frame") or child:IsA("TextButton") then child:Destroy() end
        end

        local items = {}
        pcall(function() items = getItems() end)

        local count = 0
        for name, _ in pairs(items) do
            count = count + 1
            local row = Instance.new("Frame")
            row.Size = UDim2.new(1, -8, 0, 30)
            row.LayoutOrder = count
            row.BackgroundTransparency = 1
            row.Parent = scroll

            local it = Instance.new("TextButton")
            it.Size = UDim2.new(1, 0, 1, 0)
            it.Position = UDim2.new(0, 0, 0, 0)
            it.BackgroundColor3 = colors.Window
            it.BorderSizePixel = 0
            it.AutoButtonColor = true
            it.Text = ""
            it.Active = true
            it.Selectable = true
            it.Parent = row
            round(6):Clone().Parent = it
            it.ZIndex = 52

            local nameLbl = Instance.new("TextLabel")
            nameLbl.Size = UDim2.new(1, -40, 1, 0)
            nameLbl.Position = UDim2.new(0, 8, 0, 0)
            nameLbl.BackgroundTransparency = 1
            nameLbl.Text = name
            nameLbl.TextColor3 = colors.Text
            nameLbl.Font = Enum.Font.Gotham
            nameLbl.TextSize = 13
            nameLbl.TextXAlignment = Enum.TextXAlignment.Left
            nameLbl.Parent = it
            nameLbl.ZIndex = 53

            local checkBox = Instance.new("Frame")
            checkBox.Size = UDim2.new(0, 24, 0, 24)
            checkBox.Position = UDim2.new(1, -34, 0, 3)
            checkBox.BackgroundColor3 = colors.Window
            checkBox.BorderSizePixel = 0
            checkBox.Parent = it
            round(6):Clone().Parent = checkBox
            checkBox.ZIndex = 53

            local tickFrame = Instance.new("Frame")
            tickFrame.Size = UDim2.new(1, -6, 1, -6)
            tickFrame.Position = UDim2.new(0, 3, 0, 3)
            tickFrame.BackgroundColor3 = colors.Accent
            if singleSelect then
                -- For single select, check if this item is selected
                if type(selectedTable) == "string" then
                    tickFrame.Visible = (selectedTable == name)
                else
                    tickFrame.Visible = selectedTable[name] == true
                end
            else
                -- Multi-select logic
                tickFrame.Visible = selectedTable[name] == true
            end
            tickFrame.Parent = checkBox
            round(4):Clone().Parent = tickFrame
            tickFrame.ZIndex = 54

            -- hover visuals
            it.MouseEnter:Connect(function() it.BackgroundColor3 = Color3.fromRGB(60,60,60) end)
            it.MouseLeave:Connect(function() it.BackgroundColor3 = colors.Window end)

            -- toggle selection with small debounce
            local lastToggle = 0
            it.MouseButton1Click:Connect(function()
                local now = tick()
                if now - lastToggle < 0.08 then return end
                lastToggle = now
                
                if singleSelect then
                    -- For single select, clear all others and set this one
                    if type(selectedTable) == "string" then
                        -- This is for the event seed selector - we need a callback
                        SelectedEventSeed = name
                        updateSelectedLabel()
                    else
                        -- Clear all selections
                        for k, _ in pairs(selectedTable) do
                            selectedTable[k] = false
                        end
                        selectedTable[name] = true
                        updateSelectedLabel()
                    end
                    
                    -- Update all tick frames
                    for _, otherChild in pairs(scroll:GetChildren()) do
                        if otherChild:IsA("Frame") then
                            local otherButton = otherChild:FindFirstChild("TextButton")
                            if otherButton then
                                local otherTick = otherButton:FindFirstChild("Frame")
                                if otherTick and otherTick:FindFirstChild("Frame") then
                                    local otherTickFrame = otherTick:FindFirstChild("Frame")
                                    local otherLabel = otherButton:FindFirstChild("TextLabel")
                                    if otherLabel then
                                        if type(selectedTable) == "string" then
                                            otherTickFrame.Visible = (SelectedEventSeed == otherLabel.Text)
                                        else
                                            otherTickFrame.Visible = selectedTable[otherLabel.Text] == true
                                        end
                                    end
                                end
                            end
                        end
                    end
                else
                    -- Multi-select logic
                    selectedTable[name] = not selectedTable[name]
                    tickFrame.Visible = selectedTable[name] == true
                    updateSelectedLabel()
                end
            end)
        end

        task.spawn(function()
            task.wait()
            local contentHeight = uiList.AbsoluteContentSize.Y
            local desiredW = math.max(box.AbsoluteSize.X, 240)
            local desiredH = math.min(contentHeight + 12, 300)
            local boxAbsPos = box.AbsolutePosition
            local boxSize = box.AbsoluteSize
            local px, py = clampDropdownToScreen(boxAbsPos.X, boxAbsPos.Y + boxSize.Y + 6, desiredW, desiredH)
            dropdown.Position = UDim2.fromOffset(px, py)
            dropdown.Size = UDim2.fromOffset(desiredW, desiredH)
            isPopulating = false
        end)
    end

    box.MouseButton1Click:Connect(function()
        open = not open
        dropdown.Visible = open
        if open then
            populate()
        end
    end)

    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if not open then return end
        if input.UserInputType ~= Enum.UserInputType.MouseButton1 and input.UserInputType ~= Enum.UserInputType.Touch then return end
        local pos = input.Position
        local mx, my = pos.X, pos.Y
        local absPos = dropdown.AbsolutePosition
        local absSize = dropdown.AbsoluteSize
        local bxPos = box.AbsolutePosition
        local bxSize = box.AbsoluteSize
        local inDropdown = (mx >= absPos.X and mx <= absPos.X + absSize.X and my >= absPos.Y and my <= absPos.Y + absSize.Y)
        local inBox = (mx >= bxPos.X and mx <= bxPos.X + bxSize.X and my >= bxPos.Y and my <= bxPos.Y + bxSize.Y)
        if not (inDropdown or inBox) then
            dropdown.Visible = false
            open = false
        end
    end)

    return { Container = container, Populate = populate, Dropdown = dropdown, Box = box, UpdateLabel = updateSelectedLabel, IsPopulating = function() return isPopulating end, LastPopulate = function() return lastPopulate end }
end

-- Build window (but keep hidden until ready)
local win = CreateWindow(GameInfo.Name .. " | Depso")
win.Window.Visible = false -- HIDE main GUI until ready

-- Create Tabs
local shopTab = CreateTab(win.TabContainer, "Shop", "Shop", true, 0)
local miscTab = CreateTab(win.TabContainer, "Miscellaneous", "Miscellaneous", false, 130)
local eventTab = CreateTab(win.TabContainer, "Event", "Event", false, 260)

-- Create tab contents
local function createShopContent(parent)
    local content = Instance.new("Frame")
    content.Name = "ShopContent"
    content.Size = UDim2.new(1, 0, 1, 0)
    content.BackgroundTransparency = 1
    content.Parent = parent

    -- Seeds Section (Left side)
    local seedCombo = CreateCombo(content, "Seeds", function() return GetSeedStock() end, SelectedSeeds, 8)
    local seedAutoState, seedAutoFrame = CreateCheckbox(content, "Auto-Buy Seeds Enabled", false, 95)
    
    -- Pet Eggs Section (Left side, below seeds)
    local petEggCombo = CreateCombo(content, "Pet Eggs", function() return GetPetEggStock() end, SelectedPetEggs, 135)
    local petEggAutoState, petEggAutoFrame = CreateCheckbox(content, "Auto-Buy Pet Eggs Enabled", false, 222)

    -- Gear Section (Right side)
    -- Create gear content container on the right
    local gearContainer = Instance.new("Frame")
    gearContainer.Size = UDim2.new(0.5, -12, 1, 0)
    gearContainer.Position = UDim2.new(0.5, 12, 0, 0)
    gearContainer.BackgroundTransparency = 1
    gearContainer.Parent = content

    local gearCombo = CreateCombo(gearContainer, "Gear", function() return GetGearStock() end, SelectedGear, 8)
    local gearAutoState, gearAutoFrame = CreateCheckbox(gearContainer, "Auto-Buy Gear Enabled", false, 95)

    -- Adjust seeds section to left side only
    seedCombo.Container.Size = UDim2.new(0.5, -12, 0, 80)
    seedAutoFrame.Size = UDim2.new(0.5, -12, 0, 30)
    
    -- Adjust pet eggs section to left side only
    petEggCombo.Container.Size = UDim2.new(0.5, -12, 0, 80)
    petEggAutoFrame.Size = UDim2.new(0.5, -12, 0, 30)

    return { 
        Content = content, 
        SeedCombo = seedCombo, 
        GearCombo = gearCombo, 
        PetEggCombo = petEggCombo,
        SeedAutoState = seedAutoState, 
        GearAutoState = gearAutoState,
        PetEggAutoState = petEggAutoState
    }
end

local function createMiscContent(parent)
    local content = Instance.new("Frame")
    content.Name = "MiscContent"
    content.Size = UDim2.new(1, 0, 1, 0)
    content.BackgroundTransparency = 1
    content.Visible = false
    content.Parent = parent
    
    -- Infinite Sprinkler Button
    local sprinklerBtn = CreateButton(content, "Infinite Sprinkler", 20, function()
        -- Simple infinite sprinkler - just delete sprinklers to reset effect
        local ObjectsFolder = workspace.Farm.Farm.Important.Objects_Physical
        local DeleteRemote = ReplicatedStorage:WaitForChild("GameEvents"):WaitForChild("DeleteObject")

        -- Find and delete all sprinklers
        for _, obj in ipairs(ObjectsFolder:GetChildren()) do
            if obj:IsA("Model") and obj.Name:lower():find("basic") then
                DeleteRemote:FireServer(obj)
            end
        end
    end)

    return { Content = content }
end

local function createEventContent(parent)
    local content = Instance.new("Frame")
    content.Name = "EventContent"
    content.Size = UDim2.new(1, 0, 1, 0)
    content.BackgroundTransparency = 1
    content.Visible = false
    content.Parent = parent
    
    -- Event Seed Selection
    local eventSeedCombo = CreateCombo(content, "Select Seed for Event", function() return GetSeedStock() end, SelectedEventSeed, 8, true)
    
    -- Auto Event Toggle
    local autoEventState, autoEventFrame = CreateCheckbox(content, "Auto Event (Submit Plants → Cook → Get Food → Submit Food)", false, 95)
    
    -- Status Label
    local statusLabel = CreateLabel(content, "Status: Ready", 140)
    statusLabel.TextColor3 = Color3.fromRGB(100, 200, 100)
    
    -- Plants Count Label
    local plantsCountLabel = CreateLabel(content, "Plants in queue: 0/5", 165)
    plantsCountLabel.TextColor3 = Color3.fromRGB(200, 200, 200)

    return { 
        Content = content, 
        EventSeedCombo = eventSeedCombo,
        AutoEventState = autoEventState,
        StatusLabel = statusLabel,
        PlantsCountLabel = plantsCountLabel
    }
end

-- Create tab contents
tabContents.Shop = createShopContent(win.Content)
tabContents.Miscellaneous = createMiscContent(win.Content)
tabContents.Event = createEventContent(win.Content)

-- Tab switching logic
local function switchTab(tabName)
    if currentTab == tabName then return end
    
    -- Update tab buttons
    for _, tab in pairs({shopTab, miscTab, eventTab}) do
        if tab.Name == tabName then
            tab.BackgroundColor3 = colors.Accent
        else
            tab.BackgroundColor3 = colors.TabInactive
        end
    end
    
    -- Update content visibility
    for name, tabContent in pairs(tabContents) do
        tabContent.Content.Visible = (name == tabName)
    end
    
    currentTab = tabName
end

-- Connect tab buttons
shopTab.MouseButton1Click:Connect(function() switchTab("Shop") end)
miscTab.MouseButton1Click:Connect(function() switchTab("Miscellaneous") end)
eventTab.MouseButton1Click:Connect(function() switchTab("Event") end)

-- drag & minimize
local dragging = false
local dragStart, startPos, dragConn
local isMinimized = false
local prevSize = win.Window.Size
local prevContentVisible = true

local function updateDrag(input)
    if not dragging or not dragStart or not startPos then return end
    local delta = input.Position - dragStart
    local newX = startPos.X.Offset + delta.X
    local newY = startPos.Y.Offset + delta.Y
    win.Window.Position = UDim2.new(0, newX, 0, newY)
end

win.Top.InputBegan:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseButton1 or input.UserInputType == Enum.UserInputType.Touch then
        dragging = true
        dragStart = input.Position
        startPos = win.Window.Position
        dragConn = input.Changed:Connect(function() if input.UserInputState == Enum.UserInputState.End then dragging = false end end)
    end
end)

UserInputService.InputChanged:Connect(function(input)
    if input.UserInputType == Enum.UserInputType.MouseMovement or input.UserInputType == Enum.UserInputType.Touch then
        if dragging then pcall(updateDrag, input) end
    end
end)

UserInputService.InputEnded:Connect(function(input)
    if dragConn and input.UserInputType == Enum.UserInputType.MouseButton1 then dragConn:Disconnect(); dragConn = nil end
end)

win.Minimize.MouseButton1Click:Connect(function()
    isMinimized = not isMinimized
    if isMinimized then
        prevSize = win.Window.Size
        prevContentVisible = win.Content.Visible
        TweenService:Create(win.Window, TweenInfo.new(0.18), { Size = UDim2.new(win.Window.Size.X.Scale, win.Window.Size.X.Offset, 0, win.Top.Size.Y.Offset + 6) }):Play()
        win.Content.Visible = false
        win.TabContainer.Visible = false
        win.Minimize.Text = "+"
    else
        TweenService:Create(win.Window, TweenInfo.new(0.18), { Size = prevSize }):Play()
        win.Content.Visible = prevContentVisible
        win.TabContainer.Visible = true
        win.Minimize.Text = "-"
    end
end)

-- hide combo dropdowns if window moves
win.Window:GetPropertyChangedSignal("Position"):Connect(function()
    local shopContent = tabContents.Shop
    local eventContent = tabContents.Event
    
    if shopContent then
        if shopContent.SeedCombo and shopContent.SeedCombo.Dropdown and shopContent.SeedCombo.Dropdown.Visible then
            shopContent.SeedCombo.Dropdown.Visible = false
        end
        if shopContent.GearCombo and shopContent.GearCombo.Dropdown and shopContent.GearCombo.Dropdown.Visible then
            shopContent.GearCombo.Dropdown.Visible = false
        end
        if shopContent.PetEggCombo and shopContent.PetEggCombo.Dropdown and shopContent.PetEggCombo.Dropdown.Visible then
            shopContent.PetEggCombo.Dropdown.Visible = false
        end
    end
    
    if eventContent then
        if eventContent.EventSeedCombo and eventContent.EventSeedCombo.Dropdown and eventContent.EventSeedCombo.Dropdown.Visible then
            eventContent.EventSeedCombo.Dropdown.Visible = false
        end
    end
end)

-- initial populate and wait for completion; only show main GUI after both fakeDone and populate finished
local function waitForInitialPopulateAndFake()
    -- Populate shop combos
    local shopContent = tabContents.Shop
    if shopContent then
        if shopContent.SeedCombo and shopContent.SeedCombo.Populate then
            shopContent.SeedCombo.Populate()
        end
        if shopContent.GearCombo and shopContent.GearCombo.Populate then
            shopContent.GearCombo.Populate()
        end
        if shopContent.PetEggCombo and shopContent.PetEggCombo.Populate then
            shopContent.PetEggCombo.Populate()
        end
    end
    
    -- Populate event combo
    local eventContent = tabContents.Event
    if eventContent and eventContent.EventSeedCombo and eventContent.EventSeedCombo.Populate then
        eventContent.EventSeedCombo.Populate()
    end
    
    -- Wait for populate finish or timeout
    local t0 = tick()
    local timeout = 8
    while tick() - t0 < timeout do
        local allDone = true
        if shopContent then
            if shopContent.SeedCombo and shopContent.SeedCombo.IsPopulating and shopContent.SeedCombo.IsPopulating() then
                allDone = false
            end
            if shopContent.GearCombo and shopContent.GearCombo.IsPopulating and shopContent.GearCombo.IsPopulating() then
                allDone = false
            end
            if shopContent.PetEggCombo and shopContent.PetEggCombo.IsPopulating and shopContent.PetEggCombo.IsPopulating() then
                allDone = false
            end
        end
        if eventContent and eventContent.EventSeedCombo and eventContent.EventSeedCombo.IsPopulating and eventContent.EventSeedCombo.IsPopulating() then
            allDone = false
        end
        if allDone then break end
        task.wait(0.05)
    end
    
    -- Also wait until fake timer done
    local tstart = tick()
    while not fakeDone and (tick() - tstart) < (FAKE_LOADING_SECONDS + 10) do
        task.wait(0.05)
    end
end

waitForInitialPopulateAndFake()

-- now it's safe to show main GUI
win.Window.Visible = true

-- stop loading animation and destroy loading GUI
loadingRunning = false
pcall(function() LoadingGUI:Destroy() end)

-- Optimized periodic refresh with intelligent caching and conditional updates
task.spawn(function()
    while task.wait(STOCK_UPDATE_INTERVAL) do
        pcall(function()
            ResetPerformanceStats()

            -- Only update stock if shops are likely to be open or cache is stale
            local now = tick()
            local shouldUpdateSeeds = (now - lastShopCheck.seeds) < 10 or not stockCache.seeds.isValid
            local shouldUpdateGear = (now - lastShopCheck.gear) < 10 or not stockCache.gear.isValid
            local shouldUpdatePetEggs = (now - lastShopCheck.petEggs) < 10 or not stockCache.petEggs.isValid

            -- Update stock types conditionally
            if shouldUpdateSeeds then GetSeedStock() end
            if shouldUpdateGear then GetGearStock() end
            if shouldUpdatePetEggs then GetPetEggStock() end

            -- Only update GUI if debounce allows it
            if not ShouldUpdateGUI() then return end

            -- Refresh combos for shop tab
            local shopContent = tabContents.Shop
            if shopContent then
                -- Refresh seed combo
                if shopContent.SeedCombo and shouldUpdateSeeds then
                    pcall(function()
                        local canPopulate = true
                        if shopContent.SeedCombo.IsPopulating and shopContent.SeedCombo.IsPopulating() then canPopulate = false end
                        if shopContent.SeedCombo.LastPopulate and (tick() - shopContent.SeedCombo.LastPopulate()) < 1.2 then canPopulate = false end
                        if not shopContent.SeedCombo.Dropdown.Visible then
                            shopContent.SeedCombo.Populate()
                        elseif canPopulate then
                            shopContent.SeedCombo.Populate()
                        end
                        if shopContent.SeedCombo.UpdateLabel then shopContent.SeedCombo.UpdateLabel() end
                    end)
                end

                -- Refresh gear combo
                if shopContent.GearCombo and shouldUpdateGear then
                    pcall(function()
                        local canPopulate = true
                        if shopContent.GearCombo.IsPopulating and shopContent.GearCombo.IsPopulating() then canPopulate = false end
                        if shopContent.GearCombo.LastPopulate and (tick() - shopContent.GearCombo.LastPopulate()) < 1.2 then canPopulate = false end
                        if not shopContent.GearCombo.Dropdown.Visible then
                            shopContent.GearCombo.Populate()
                        elseif canPopulate then
                            shopContent.GearCombo.Populate()
                        end
                        if shopContent.GearCombo.UpdateLabel then shopContent.GearCombo.UpdateLabel() end
                    end)
                end

                -- Refresh pet egg combo
                if shopContent.PetEggCombo and shouldUpdatePetEggs then
                    pcall(function()
                        local canPopulate = true
                        if shopContent.PetEggCombo.IsPopulating and shopContent.PetEggCombo.IsPopulating() then canPopulate = false end
                        if shopContent.PetEggCombo.LastPopulate and (tick() - shopContent.PetEggCombo.LastPopulate()) < 1.2 then canPopulate = false end
                        if not shopContent.PetEggCombo.Dropdown.Visible then
                            shopContent.PetEggCombo.Populate()
                        elseif canPopulate then
                            shopContent.PetEggCombo.Populate()
                        end
                        if shopContent.PetEggCombo.UpdateLabel then shopContent.PetEggCombo.UpdateLabel() end
                    end)
                end
            end

            -- Refresh event combo (only if seeds were updated)
            local eventContent = tabContents.Event
            if eventContent and eventContent.EventSeedCombo and shouldUpdateSeeds then
                pcall(function()
                    local canPopulate = true
                    if eventContent.EventSeedCombo.IsPopulating and eventContent.EventSeedCombo.IsPopulating() then canPopulate = false end
                    if eventContent.EventSeedCombo.LastPopulate and (tick() - eventContent.EventSeedCombo.LastPopulate()) < 1.2 then canPopulate = false end
                    if not eventContent.EventSeedCombo.Dropdown.Visible then
                        eventContent.EventSeedCombo.Populate()
                    elseif canPopulate then
                        eventContent.EventSeedCombo.Populate()
                    end
                    if eventContent.EventSeedCombo.UpdateLabel then eventContent.EventSeedCombo.UpdateLabel() end
                end)
            end
        end)
    end
end)

-- Optimized auto-buy loops with early exit conditions
task.spawn(function()
    while task.wait(1) do
        local shopContent = tabContents.Shop
        if not shopContent then continue end

        -- Early exit if no items are selected for any category
        if not HasSelectedItems("seeds") and not HasSelectedItems("gear") and not HasSelectedItems("petEggs") then
            continue
        end

        -- Auto-buy seeds if enabled and items selected
        if shopContent.SeedAutoState and shopContent.SeedAutoState.Value and HasSelectedItems("seeds") then
            pcall(BuyAllSelectedSeeds)
        end

        -- Auto-buy gear if enabled and items selected
        if shopContent.GearAutoState and shopContent.GearAutoState.Value and HasSelectedItems("gear") then
            pcall(BuyAllSelectedGear)
        end

        -- Auto-buy pet eggs if enabled and items selected
        if shopContent.PetEggAutoState and shopContent.PetEggAutoState.Value and HasSelectedItems("petEggs") then
            pcall(BuyAllSelectedPetEggs)
        end
    end
end)

-- Optimized Event automation loop with state-based processing
task.spawn(function()
    local eventPhase = "submit_plants" -- "submit_plants", "cooking", "get_food", "submit_food"
    local lastAction = 0
    local lastStatusUpdate = 0
    local STATUS_UPDATE_INTERVAL = 2 -- Update status every 2 seconds instead of every loop

    while task.wait(EVENT_LOOP_INTERVAL) do
        local eventContent = tabContents.Event

        -- Early exit if automation is disabled
        if not eventContent or not eventContent.AutoEventState or not eventContent.AutoEventState.Value then
            -- Reset phase when disabled to avoid state issues
            if eventPhase ~= "submit_plants" then
                eventPhase = "submit_plants"
                PlantsInPot = 0
            end
            continue
        end

        -- Early exit if no seed selected
        if SelectedEventSeed == "" then
            local now = tick()
            if now - lastStatusUpdate > STATUS_UPDATE_INTERVAL and eventContent.StatusLabel then
                eventContent.StatusLabel.Text = "Status: Please select a seed"
                eventContent.StatusLabel.TextColor3 = Color3.fromRGB(200, 100, 100)
                lastStatusUpdate = now
            end
            continue
        end

        local now = tick()

        -- Update plants count and status less frequently
        if now - lastStatusUpdate > STATUS_UPDATE_INTERVAL then
            if eventContent.PlantsCountLabel then
                eventContent.PlantsCountLabel.Text = "Plants in queue: " .. PlantsInPot .. "/5"
            end
            lastStatusUpdate = now
        end

        -- Phase 1: Submit Plants (up to 5) - Optimized with reduced wait times
        if eventPhase == "submit_plants" then
            if now - lastStatusUpdate <= STATUS_UPDATE_INTERVAL and eventContent.StatusLabel then
                eventContent.StatusLabel.Text = "Status: Adding plants to pot (" .. PlantsInPot .. "/5)"
                eventContent.StatusLabel.TextColor3 = Color3.fromRGB(200, 200, 100)
            end

            -- Optimized plant submission with reduced wait times
            if PlantsInPot < 5 and (now - lastAction) > 0.8 then -- Reduced from 1 second
                if FindAndEquipSeed(SelectedEventSeed) then
                    task.wait(0.15) -- Reduced wait time for equip
                    SubmitPlant()
                    PlantsInPot = PlantsInPot + 1
                    lastAction = now
                end
            end

            if PlantsInPot >= 5 then
                CookBest()
                eventPhase = "cooking"
                lastAction = now
            end

        -- Phase 2: Wait for cooking to finish - Optimized with less frequent status updates
        elseif eventPhase == "cooking" then
            -- Only check cooking status, don't update UI every loop
            local isCookingFinished = IsCookingFinished()

            if now - lastStatusUpdate <= STATUS_UPDATE_INTERVAL and eventContent.StatusLabel then
                eventContent.StatusLabel.Text = "Status: Cooking in progress..."
                eventContent.StatusLabel.TextColor3 = Color3.fromRGB(200, 150, 100)
            end

            if isCookingFinished and (now - lastAction) > 0.8 then -- Reduced from 1 second
                GetFoodFromPot()
                eventPhase = "get_food"
                lastAction = now
            end

        -- Phase 3: Get food and submit - Optimized timing
        elseif eventPhase == "get_food" then
            if now - lastStatusUpdate <= STATUS_UPDATE_INTERVAL and eventContent.StatusLabel then
                eventContent.StatusLabel.Text = "Status: Getting food from pot..."
                eventContent.StatusLabel.TextColor3 = Color3.fromRGB(100, 200, 100)
            end

            if (now - lastAction) > 1.2 then -- Reduced from 1.5 seconds
                SubmitFood()
                eventPhase = "submit_food"
                lastAction = now
            end

        -- Phase 4: Submit food and reset - Optimized timing
        elseif eventPhase == "submit_food" then
            if now - lastStatusUpdate <= STATUS_UPDATE_INTERVAL and eventContent.StatusLabel then
                eventContent.StatusLabel.Text = "Status: Submitting food..."
                eventContent.StatusLabel.TextColor3 = Color3.fromRGB(100, 200, 150)
            end

            if (now - lastAction) > 0.8 then -- Reduced from 1 second
                -- Reset for next cycle
                PlantsInPot = 0
                eventPhase = "submit_plants"
                lastAction = now

                if eventContent.StatusLabel then
                    eventContent.StatusLabel.Text = "Status: Cycle complete - starting new cycle"
                    eventContent.StatusLabel.TextColor3 = Color3.fromRGB(100, 200, 100)
                end
            end
        end
    end
end)